@tailwind base;
@tailwind components;
@tailwind utilities;

:root {
  --radius: 0.625rem;
  --background: 0 0% 100%;
  --foreground: 0 0% 14.5%;
  --card: 0 0% 100%;
  --card-foreground: 0 0% 14.5%;
  --popover: 0 0% 100%;
  --popover-foreground: 0 0% 14.5%;
  --primary: 0 0% 20.5%;
  --primary-foreground: 0 0% 98.5%;
  --secondary: 0 0% 97%;
  --secondary-foreground: 0 0% 20.5%;
  --muted: 0 0% 97%;
  --muted-foreground: 0 0% 55.6%;
  --accent: 0 0% 97%;
  --accent-foreground: 0 0% 20.5%;
  --destructive: 27 24.5% 57.7%;
  --border: 0 0% 92.2%;
  --input: 0 0% 92.2%;
  --ring: 0 0% 70.8%;
  --chart-1: 41 22.2% 64.6%;
  --chart-2: 184 11.8% 60%;
  --chart-3: 227 7% 39.8%;
  --chart-4: 84 18.9% 82.8%;
  --chart-5: 70 18.8% 76.9%;
  --sidebar: 0 0% 98.5%;
  --sidebar-foreground: 0 0% 14.5%;
  --sidebar-primary: 0 0% 20.5%;
  --sidebar-primary-foreground: 0 0% 98.5%;
  --sidebar-accent: 0 0% 97%;
  --sidebar-accent-foreground: 0 0% 20.5%;
  --sidebar-border: 0 0% 92.2%;
  --sidebar-ring: 0 0% 70.8%;
}

.dark {
  --background: 0 0% 14.5%;
  --foreground: 0 0% 98.5%;
  --card: 0 0% 20.5%;
  --card-foreground: 0 0% 98.5%;
  --popover: 0 0% 20.5%;
  --popover-foreground: 0 0% 98.5%;
  --primary: 0 0% 92.2%;
  --primary-foreground: 0 0% 20.5%;
  --secondary: 0 0% 26.9%;
  --secondary-foreground: 0 0% 98.5%;
  --muted: 0 0% 26.9%;
  --muted-foreground: 0 0% 70.8%;
  --accent: 0 0% 26.9%;
  --accent-foreground: 0 0% 98.5%;
  --destructive: 22 19.1% 70.4%;
  --border: 0 0% 100% / 10%;
  --input: 0 0% 100% / 15%;
  --ring: 0 0% 55.6%;
  --chart-1: 264 24.3% 48.8%;
  --chart-2: 162 17% 69.6%;
  --chart-3: 70 18.8% 76.9%;
  --chart-4: 303 26.5% 62.7%;
  --chart-5: 16 24.6% 64.5%;
  --sidebar: 0 0% 20.5%;
  --sidebar-foreground: 0 0% 98.5%;
  --sidebar-primary: 0 0% 20.5%;
  --sidebar-primary-foreground: 0 0% 98.5%;
  --sidebar-accent: 0 0% 26.9%;
  --sidebar-accent-foreground: 0 0% 98.5%;
  --sidebar-border: 0 0% 100% / 10%;
  --sidebar-ring: 0 0% 55.6%;
}

@layer base {
  * {
    @apply border-border outline-ring/50;
  }
  html {
    background-color: hsl(var(--background)) !important;
    color: hsl(var(--foreground)) !important;
  }
  body {
    background-color: hsl(var(--background));
    color: hsl(var(--foreground));
  }

  /* Add a clear focus indicator */
  :focus-visible {
    outline: 3px solid hsl(41 22.2% 64.6%);
    outline-offset: 2px;
    border-color: transparent;
  }

  /* Force dropdown menus to appear above everything */
  [data-radix-popper-content-wrapper] {
    z-index: 9999 !important;
  }

  [role="menu"] {
    z-index: 9999 !important;
  }
}

/* Import layout protection styles */
@import '../styles/layout-protection.css';

/* Import enhanced accessibility and responsive utilities */
@import '../styles/accessibility-responsive.css';
